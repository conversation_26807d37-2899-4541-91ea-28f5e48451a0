/**
 * QR Library Modular - Versão compilada para navegador
 * Biblioteca modular e extensível para geração de QR codes
 *
 * Inclui:
 * - Core da API modular
 * - Sistema de plugins
 * - Plugin para qrcode-generator
 * - Plugin para qr-code-styling
 * - Utilitários e tipos
 */

(function (global) {
  'use strict';

  // Verificar dependências
  if (typeof window !== 'undefined') {
    if (!window.qrcode) {
      console.warn(
        'QR Library: qrcode-generator não encontrado. Algumas funcionalidades podem não funcionar.'
      );
    }
    if (!window.QRCodeStyling) {
      console.warn(
        'QR Library: QRCodeStyling não encontrado. Funcionalidades avançadas podem não funcionar.'
      );
    }
  }

  // === TIPOS E CONSTANTES ===
  const EXPORT_FORMATS = {
    PNG: 'png',
    JPEG: 'jpeg',
    SVG: 'svg',
    WEBP: 'webp',
  };

  const QR_TYPES = {
    CANVAS: 'canvas',
    SVG: 'svg',
  };

  const ERROR_CORRECTION_LEVELS = {
    L: 'L',
    M: 'M',
    Q: 'Q',
    H: 'H',
  };

  const ENCODING_MODES = {
    NUMERIC: 'Numeric',
    ALPHANUMERIC: 'Alphanumeric',
    BYTE: 'Byte',
    KANJI: 'Kanji',
  };

  const DOT_TYPES = {
    SQUARE: 'square',
    DOTS: 'dots',
    ROUNDED: 'rounded',
    EXTRA_ROUNDED: 'extra-rounded',
    CLASSY: 'classy',
    CLASSY_ROUNDED: 'classy-rounded',
  };

  const CORNER_SQUARE_TYPES = {
    SQUARE: 'square',
    DOT: 'dot',
    EXTRA_ROUNDED: 'extra-rounded',
  };

  const CORNER_DOT_TYPES = {
    SQUARE: 'square',
    DOT: 'dot',
  };

  const GRADIENT_TYPES = {
    LINEAR: 'linear',
    RADIAL: 'radial',
  };

  const EVENTS = {
    QR_GENERATED: 'qr:generated',
    QR_RENDERED: 'qr:rendered',
    QR_EXPORTED: 'qr:exported',
    QR_UPDATED: 'qr:updated',
    PLUGIN_LOADED: 'plugin:loaded',
    PLUGIN_UNLOADED: 'plugin:unloaded',
    ERROR: 'error',
  };

  const DEFAULT_QR_OPTIONS = {
    width: 300,
    height: 300,
    type: QR_TYPES.CANVAS,
    data: '',
    margin: 0,
    qrOptions: {
      typeNumber: 0,
      mode: ENCODING_MODES.BYTE,
      errorCorrectionLevel: ERROR_CORRECTION_LEVELS.Q,
    },
    imageOptions: {
      hideBackgroundDots: true,
      imageSize: 0.4,
      margin: 0,
      crossOrigin: 'anonymous',
    },
    dotsOptions: {
      type: DOT_TYPES.SQUARE,
      color: '#000000',
    },
    backgroundOptions: {
      color: '#ffffff',
    },
  };

  // === UTILITÁRIOS ===
  class EventEmitter {
    constructor() {
      this.events = new Map();
    }

    on(event, listener) {
      if (!this.events.has(event)) {
        this.events.set(event, []);
      }
      this.events.get(event).push(listener);
      return () => this.off(event, listener);
    }

    once(event, listener) {
      const onceListener = (...args) => {
        this.off(event, onceListener);
        listener(...args);
      };
      return this.on(event, onceListener);
    }

    off(event, listener) {
      if (!this.events.has(event)) return;
      const listeners = this.events.get(event);
      const index = listeners.indexOf(listener);
      if (index > -1) {
        listeners.splice(index, 1);
      }
      if (listeners.length === 0) {
        this.events.delete(event);
      }
    }

    emit(event, ...args) {
      if (!this.events.has(event)) return;
      const listeners = this.events.get(event).slice();
      for (const listener of listeners) {
        try {
          listener(...args);
        } catch (error) {
          console.error(`Erro no listener do evento '${event}':`, error);
        }
      }
    }

    removeAllListeners(event) {
      if (event) {
        this.events.delete(event);
      } else {
        this.events.clear();
      }
    }
  }

  class Logger {
    constructor(name = 'QRLibrary') {
      this.name = name;
      this.level = 2; // INFO
      this.enabled = false; // Desabilitado por padrão
    }

    setLevel(level) {
      this.level = level;
    }
    setEnabled(enabled) {
      this.enabled = enabled;
    }

    formatMessage(level, message) {
      const timestamp = new Date().toISOString();
      return `[${timestamp}] [${this.name}] [${level}] ${message}`;
    }

    error(message, ...args) {
      if (this.enabled && this.level >= 0) {
        console.error(this.formatMessage('ERROR', message), ...args);
      }
    }

    warn(message, ...args) {
      if (this.enabled && this.level >= 1) {
        console.warn(this.formatMessage('WARN', message), ...args);
      }
    }

    info(message, ...args) {
      if (this.enabled && this.level >= 2) {
        console.info(this.formatMessage('INFO', message), ...args);
      }
    }

    debug(message, ...args) {
      if (this.enabled && this.level >= 3) {
        console.debug(this.formatMessage('DEBUG', message), ...args);
      }
    }

    child(childName) {
      const child = new Logger(`${this.name}:${childName}`);
      child.setLevel(this.level);
      child.setEnabled(this.enabled);
      return child;
    }
  }

  // === INTERFACE DE PLUGIN ===
  class IQRPlugin {
    getName() {
      throw new Error('Plugin deve implementar getName()');
    }
    getVersion() {
      throw new Error('Plugin deve implementar getVersion()');
    }
    getDependencies() {
      return [];
    }
    async initialize(config = {}) {
      throw new Error('Plugin deve implementar initialize()');
    }
    async generateQR(options) {
      throw new Error('Plugin deve implementar generateQR()');
    }
    async render(container, qrResult) {
      throw new Error('Plugin deve implementar render()');
    }
    async export(qrResult, format, exportOptions = {}) {
      throw new Error('Plugin deve implementar export()');
    }
    async update(qrResult, newOptions) {
      throw new Error('Plugin deve implementar update()');
    }
    async cleanup() {}
    validateOptions(options) {
      return true;
    }
    getCapabilities() {
      return {
        formats: ['png', 'jpeg', 'svg'],
        features: [],
        maxSize: 1000,
        minSize: 100,
      };
    }
  }

  // === GERENCIADOR DE PLUGINS ===
  class PluginManager {
    constructor() {
      this.plugins = new Map();
      this.loadedPlugins = new Map();
      this.dependencies = new Map();
    }

    async registerPlugin(plugin) {
      const name = plugin.getName();
      if (this.plugins.has(name)) {
        throw new Error(`Plugin '${name}' já está registrado`);
      }

      const dependencies = plugin.getDependencies();
      for (const dep of dependencies) {
        if (!this.plugins.has(dep)) {
          throw new Error(
            `Plugin '${name}' requer dependência '${dep}' que não está registrada`
          );
        }
      }

      this.plugins.set(name, plugin);
      this.dependencies.set(name, dependencies);
      // Plugin registrado silenciosamente
    }

    async loadPlugin(pluginName, config = {}) {
      if (this.loadedPlugins.has(pluginName)) {
        return this.loadedPlugins.get(pluginName);
      }

      if (!this.plugins.has(pluginName)) {
        throw new Error(`Plugin '${pluginName}' não está registrado`);
      }

      const plugin = this.plugins.get(pluginName);
      const dependencies = this.dependencies.get(pluginName) || [];

      for (const dep of dependencies) {
        await this.loadPlugin(dep, config[dep] || {});
      }

      await plugin.initialize(config);
      this.loadedPlugins.set(pluginName, plugin);
      // Plugin carregado silenciosamente
      return plugin;
    }

    getPlugin(pluginName) {
      return this.loadedPlugins.get(pluginName) || null;
    }

    getBestPlugin(options) {
      const loadedPlugins = Array.from(this.loadedPlugins.values());
      const compatiblePlugins = loadedPlugins.filter((plugin) =>
        plugin.validateOptions(options)
      );
      return compatiblePlugins.length > 0 ? compatiblePlugins[0] : null;
    }

    listPlugins() {
      return Array.from(this.plugins.entries()).map(([name, plugin]) => ({
        name,
        version: plugin.getVersion(),
        dependencies: plugin.getDependencies(),
        loaded: this.loadedPlugins.has(name),
        capabilities: plugin.getCapabilities(),
      }));
    }

    async unloadAllPlugins() {
      const pluginNames = Array.from(this.loadedPlugins.keys());
      for (const name of pluginNames) {
        const plugin = this.loadedPlugins.get(name);
        await plugin.cleanup();
        this.loadedPlugins.delete(name);
      }
    }
  }

  // === CORE DA BIBLIOTECA ===
  class QRLibraryCore {
    constructor() {
      this.pluginManager = new PluginManager();
      this.currentQR = null;
      this.currentPlugin = null;
      this.defaultOptions = DEFAULT_QR_OPTIONS;
    }

    async registerPlugin(plugin) {
      await this.pluginManager.registerPlugin(plugin);
    }

    async loadPlugin(pluginName, config = {}) {
      await this.pluginManager.loadPlugin(pluginName, config);
    }

    async generateQR(options = {}) {
      const mergedOptions = { ...this.defaultOptions, ...options };
      const plugin = this.pluginManager.getBestPlugin(mergedOptions);

      if (!plugin) {
        throw new Error(
          'Nenhum plugin compatível encontrado para as opções fornecidas'
        );
      }

      this.currentQR = await plugin.generateQR(mergedOptions);
      this.currentPlugin = plugin;

      return {
        qrResult: this.currentQR,
        plugin: plugin.getName(),
        options: mergedOptions,
      };
    }

    async render(container, qrData = null) {
      const data = qrData || {
        qrResult: this.currentQR,
        plugin: this.currentPlugin,
      };

      if (!data.qrResult || !data.plugin) {
        throw new Error('Nenhum QR Code foi gerado ainda');
      }

      const plugin =
        typeof data.plugin === 'string'
          ? this.pluginManager.getPlugin(data.plugin)
          : data.plugin;

      if (!plugin) {
        throw new Error('Plugin não encontrado');
      }

      await plugin.render(container, data.qrResult);
    }

    async export(format = 'png', exportOptions = {}, qrData = null) {
      const data = qrData || {
        qrResult: this.currentQR,
        plugin: this.currentPlugin,
      };

      if (!data.qrResult || !data.plugin) {
        throw new Error('Nenhum QR Code foi gerado ainda');
      }

      const plugin =
        typeof data.plugin === 'string'
          ? this.pluginManager.getPlugin(data.plugin)
          : data.plugin;

      if (!plugin) {
        throw new Error('Plugin não encontrado');
      }

      return await plugin.export(data.qrResult, format, exportOptions);
    }

    async download(filename = 'qrcode', format = 'png', exportOptions = {}) {
      const data = await this.export(format, exportOptions);
      const url =
        format === 'svg'
          ? `data:image/svg+xml;charset=utf-8,${encodeURIComponent(data)}`
          : URL.createObjectURL(data);

      const link = document.createElement('a');
      link.href = url;
      link.download = `${filename}.${format}`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      if (format !== 'svg') {
        URL.revokeObjectURL(url);
      }
    }

    listPlugins() {
      return this.pluginManager.listPlugins();
    }

    clear() {
      this.currentQR = null;
      this.currentPlugin = null;
    }

    async cleanup() {
      this.clear();
      await this.pluginManager.unloadAllPlugins();
    }
  }

  // === PLUGINS ===

  // Plugin QRCode Generator
  class QRCodeGeneratorPlugin extends IQRPlugin {
    constructor() {
      super();
      this.logger = new Logger('QRCodeGeneratorPlugin');
      this.qrcode = null;
      this.initialized = false;
    }

    getName() {
      return 'qrcode-generator';
    }
    getVersion() {
      return '1.4.4';
    }
    getDependencies() {
      return [];
    }

    async initialize(config = {}) {
      if (this.initialized) return;

      if (typeof window !== 'undefined' && window.qrcode) {
        this.qrcode = window.qrcode;
      } else if (typeof global !== 'undefined' && global.qrcode) {
        this.qrcode = global.qrcode;
      } else {
        throw new Error('Biblioteca qrcode-generator não encontrada');
      }

      this.initialized = true;
      this.logger.info('Plugin qrcode-generator inicializado');
    }

    async generateQR(options) {
      if (!this.initialized) throw new Error('Plugin não foi inicializado');
      if (!options.data)
        throw new Error('Dados para o QR Code são obrigatórios');

      const typeNumber = options.qrOptions?.typeNumber || 0;
      const errorCorrectionLevel =
        options.qrOptions?.errorCorrectionLevel || ERROR_CORRECTION_LEVELS.Q;
      const mode = options.qrOptions?.mode || ENCODING_MODES.BYTE;

      const qr = this.qrcode(typeNumber, errorCorrectionLevel);
      qr.addData(options.data, mode);
      qr.make();

      return {
        qrInstance: qr,
        data: options.data,
        options,
        moduleCount: qr.getModuleCount(),
        typeNumber: typeNumber,
        errorCorrectionLevel,
        mode,
      };
    }

    async render(container, qrResult) {
      const { qrInstance, options } = qrResult;
      const cellSize = Math.floor(
        (options.width || 300) / qrInstance.getModuleCount()
      );
      const margin = options.margin || 0;

      container.innerHTML = '';
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      const size = qrInstance.getModuleCount() * cellSize + margin * 2;

      canvas.width = size;
      canvas.height = size;

      ctx.fillStyle = options.backgroundOptions?.color || '#ffffff';
      ctx.fillRect(0, 0, size, size);
      ctx.fillStyle = options.dotsOptions?.color || '#000000';

      for (let row = 0; row < qrInstance.getModuleCount(); row++) {
        for (let col = 0; col < qrInstance.getModuleCount(); col++) {
          if (qrInstance.isDark(row, col)) {
            ctx.fillRect(
              col * cellSize + margin,
              row * cellSize + margin,
              cellSize,
              cellSize
            );
          }
        }
      }

      container.appendChild(canvas);
    }

    async export(qrResult, format, exportOptions = {}) {
      const { qrInstance, options } = qrResult;

      if (format === EXPORT_FORMATS.SVG) {
        return this.exportSVG(qrInstance, options);
      } else {
        return this.exportCanvas(qrInstance, options, format, exportOptions);
      }
    }

    exportSVG(qrInstance, options) {
      const cellSize = Math.floor(
        (options.width || 300) / qrInstance.getModuleCount()
      );
      const margin = options.margin || 0;
      const size = qrInstance.getModuleCount() * cellSize + margin * 2;

      let svg = `<svg width="${size}" height="${size}" xmlns="http://www.w3.org/2000/svg">`;

      const bgColor = options.backgroundOptions?.color || '#ffffff';
      if (bgColor !== 'transparent') {
        svg += `<rect width="${size}" height="${size}" fill="${bgColor}"/>`;
      }

      const dotColor = options.dotsOptions?.color || '#000000';
      for (let row = 0; row < qrInstance.getModuleCount(); row++) {
        for (let col = 0; col < qrInstance.getModuleCount(); col++) {
          if (qrInstance.isDark(row, col)) {
            const x = col * cellSize + margin;
            const y = row * cellSize + margin;
            svg += `<rect x="${x}" y="${y}" width="${cellSize}" height="${cellSize}" fill="${dotColor}"/>`;
          }
        }
      }

      svg += '</svg>';
      return svg;
    }

    async exportCanvas(qrInstance, options, format, exportOptions) {
      return new Promise((resolve, reject) => {
        const cellSize = Math.floor(
          (options.width || 300) / qrInstance.getModuleCount()
        );
        const margin = options.margin || 0;
        const size = qrInstance.getModuleCount() * cellSize + margin * 2;

        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        canvas.width = size;
        canvas.height = size;

        ctx.fillStyle = options.backgroundOptions?.color || '#ffffff';
        ctx.fillRect(0, 0, size, size);
        ctx.fillStyle = options.dotsOptions?.color || '#000000';

        for (let row = 0; row < qrInstance.getModuleCount(); row++) {
          for (let col = 0; col < qrInstance.getModuleCount(); col++) {
            if (qrInstance.isDark(row, col)) {
              ctx.fillRect(
                col * cellSize + margin,
                row * cellSize + margin,
                cellSize,
                cellSize
              );
            }
          }
        }

        canvas.toBlob(
          (blob) => {
            blob ? resolve(blob) : reject(new Error('Falha ao gerar blob'));
          },
          `image/${format}`,
          exportOptions.quality || 0.9
        );
      });
    }

    async update(qrResult, newOptions) {
      const mergedOptions = { ...qrResult.options, ...newOptions };
      return await this.generateQR(mergedOptions);
    }

    validateOptions(options) {
      if (!options.data) return false;
      if (options.dotsOptions?.type && options.dotsOptions.type !== 'square')
        return false;
      if (options.dotsOptions?.gradient || options.backgroundOptions?.gradient)
        return false;
      // Não usar este plugin se há opções de cantos definidas
      if (options.cornersSquareOptions || options.cornersDotOptions)
        return false;
      return true;
    }

    getCapabilities() {
      return {
        formats: [EXPORT_FORMATS.PNG, EXPORT_FORMATS.JPEG, EXPORT_FORMATS.SVG],
        features: ['basic-qr', 'solid-colors'],
        dotTypes: ['square'],
        gradients: false,
        corners: false,
        images: false,
        maxSize: 1000,
        minSize: 50,
      };
    }

    async cleanup() {
      this.qrcode = null;
      this.initialized = false;
    }
  }

  // Plugin QRCode Styling
  class QRCodeStylingPlugin extends IQRPlugin {
    constructor() {
      super();
      this.logger = new Logger('QRCodeStylingPlugin');
      this.QRCodeStyling = null;
      this.initialized = false;
    }

    getName() {
      return 'qr-code-styling';
    }
    getVersion() {
      return '1.8.3';
    }
    getDependencies() {
      return ['qrcode-generator'];
    }

    async initialize(config = {}) {
      if (this.initialized) return;

      if (typeof window !== 'undefined' && window.QRCodeStyling) {
        this.QRCodeStyling = window.QRCodeStyling;
      } else if (typeof global !== 'undefined' && global.QRCodeStyling) {
        this.QRCodeStyling = global.QRCodeStyling;
      } else {
        throw new Error('Biblioteca QRCodeStyling não encontrada');
      }

      this.initialized = true;
      this.logger.info('Plugin qr-code-styling inicializado');
    }

    async generateQR(options) {
      if (!this.initialized) throw new Error('Plugin não foi inicializado');
      if (!options.data)
        throw new Error('Dados para o QR Code são obrigatórios');

      const qrOptions = this.convertOptions(options);
      const qrCodeStyling = new this.QRCodeStyling(qrOptions);

      return {
        qrInstance: qrCodeStyling,
        originalOptions: options,
        convertedOptions: qrOptions,
        data: options.data,
      };
    }

    async render(container, qrResult) {
      const { qrInstance } = qrResult;
      container.innerHTML = '';
      qrInstance.append(container);
    }

    async export(qrResult, format, exportOptions = {}) {
      const { qrInstance } = qrResult;
      return await qrInstance.getRawData(format);
    }

    async update(qrResult, newOptions) {
      const { qrInstance, originalOptions } = qrResult;
      const mergedOptions = { ...originalOptions, ...newOptions };
      const convertedOptions = this.convertOptions(mergedOptions);

      qrInstance.update(convertedOptions);

      return {
        ...qrResult,
        originalOptions: mergedOptions,
        convertedOptions,
      };
    }

    convertOptions(options) {
      const converted = {
        width: options.width || 300,
        height: options.height || 300,
        type: options.type || 'canvas',
        data: options.data,
        margin: options.margin || 0,
      };

      if (options.qrOptions) {
        converted.qrOptions = {
          typeNumber: options.qrOptions.typeNumber || 0,
          mode: options.qrOptions.mode || 'Byte',
          errorCorrectionLevel: options.qrOptions.errorCorrectionLevel || 'Q',
        };
      }

      if (options.imageOptions || options.image) {
        converted.imageOptions = {
          hideBackgroundDots:
            options.imageOptions?.hideBackgroundDots !== false,
          imageSize: options.imageOptions?.imageSize || 0.4,
          margin: options.imageOptions?.margin || 0,
          crossOrigin: options.imageOptions?.crossOrigin || 'anonymous',
        };

        if (options.image) {
          converted.image = options.image;
        }
      }

      if (options.dotsOptions) {
        converted.dotsOptions = {
          type: options.dotsOptions.type || 'square',
          color: options.dotsOptions.color || '#000000',
        };

        if (options.dotsOptions.gradient) {
          converted.dotsOptions.gradient = this.convertGradient(
            options.dotsOptions.gradient
          );
          delete converted.dotsOptions.color;
        }
      }

      if (options.backgroundOptions) {
        converted.backgroundOptions = {
          color: options.backgroundOptions.color || '#ffffff',
        };

        if (options.backgroundOptions.gradient) {
          converted.backgroundOptions.gradient = this.convertGradient(
            options.backgroundOptions.gradient
          );
          delete converted.backgroundOptions.color;
        }
      }

      if (options.cornersSquareOptions) {
        converted.cornersSquareOptions = {
          type: options.cornersSquareOptions.type,
          color: options.cornersSquareOptions.color || '#000000',
        };

        if (options.cornersSquareOptions.gradient) {
          converted.cornersSquareOptions.gradient = this.convertGradient(
            options.cornersSquareOptions.gradient
          );
          delete converted.cornersSquareOptions.color;
        }
      }

      if (options.cornersDotOptions) {
        converted.cornersDotOptions = {
          type: options.cornersDotOptions.type,
          color: options.cornersDotOptions.color || '#000000',
        };

        if (options.cornersDotOptions.gradient) {
          converted.cornersDotOptions.gradient = this.convertGradient(
            options.cornersDotOptions.gradient
          );
          delete converted.cornersDotOptions.color;
        }
      }

      return converted;
    }

    convertGradient(gradient) {
      return {
        type: gradient.type || 'linear',
        rotation: gradient.rotation || 0,
        colorStops: gradient.colorStops || [
          { offset: 0, color: gradient.color1 || '#000000' },
          { offset: 1, color: gradient.color2 || '#000000' },
        ],
      };
    }

    validateOptions(options) {
      return true; // Suporta todas as funcionalidades
    }

    getCapabilities() {
      return {
        formats: [
          EXPORT_FORMATS.PNG,
          EXPORT_FORMATS.JPEG,
          EXPORT_FORMATS.SVG,
          EXPORT_FORMATS.WEBP,
        ],
        features: [
          'advanced-styling',
          'gradients',
          'custom-dots',
          'corner-styling',
          'images',
          'transparency',
        ],
        dotTypes: Object.values(DOT_TYPES),
        cornerSquareTypes: Object.values(CORNER_SQUARE_TYPES),
        cornerDotTypes: Object.values(CORNER_DOT_TYPES),
        gradientTypes: Object.values(GRADIENT_TYPES),
        gradients: true,
        corners: true,
        images: true,
        maxSize: 2000,
        minSize: 50,
      };
    }

    async cleanup() {
      this.QRCodeStyling = null;
      this.initialized = false;
    }
  }

  // === CLASSE PRINCIPAL ===
  class QRLibrary extends EventEmitter {
    constructor(options = {}) {
      super();
      this.core = new QRLibraryCore();
      this.logger = new Logger('QRLibrary');
      this.options = { ...DEFAULT_QR_OPTIONS, ...options };
      this.initialized = false;

      if (options.logLevel !== undefined) {
        this.logger.setLevel(options.logLevel);
      }
      if (options.enableLogging !== undefined) {
        this.logger.setEnabled(options.enableLogging);
      }
    }

    async initialize(config = {}) {
      if (this.initialized) {
        this.logger.warn('Biblioteca já foi inicializada');
        return;
      }

      try {
        this.logger.info('Inicializando QR Library...');

        if (config.defaultOptions) {
          this.options = { ...this.options, ...config.defaultOptions };
        }

        this.initialized = true;
        this.logger.info('QR Library inicializada com sucesso');
        this.emit(EVENTS.PLUGIN_LOADED, { library: 'QRLibrary' });
      } catch (error) {
        this.logger.error('Erro ao inicializar biblioteca:', error);
        this.emit(EVENTS.ERROR, error);
        throw error;
      }
    }

    async registerPlugin(plugin) {
      try {
        await this.core.registerPlugin(plugin);
        this.logger.info(`Plugin ${plugin.getName()} registrado`);
        this.emit(EVENTS.PLUGIN_LOADED, { plugin: plugin.getName() });
      } catch (error) {
        this.logger.error('Erro ao registrar plugin:', error);
        this.emit(EVENTS.ERROR, error);
        throw error;
      }
    }

    async loadPlugin(pluginName, config = {}) {
      try {
        await this.core.loadPlugin(pluginName, config);
        this.logger.info(`Plugin ${pluginName} carregado`);
      } catch (error) {
        this.logger.error(`Erro ao carregar plugin ${pluginName}:`, error);
        this.emit(EVENTS.ERROR, error);
        throw error;
      }
    }

    async generate(options = {}) {
      if (!this.initialized) {
        await this.initialize();
      }

      const mergedOptions = { ...this.options, ...options };

      try {
        this.logger.debug('Gerando QR Code com opções:', mergedOptions);
        const result = await this.core.generateQR(mergedOptions);
        this.logger.info(`QR Code gerado usando plugin ${result.plugin}`);
        this.emit(EVENTS.QR_GENERATED, result);
        return result;
      } catch (error) {
        this.logger.error('Erro ao gerar QR Code:', error);
        this.emit(EVENTS.ERROR, error);
        throw error;
      }
    }

    async render(container, qrData = null) {
      if (!container || !container.nodeType) {
        throw new Error('Container deve ser um elemento HTML válido');
      }

      try {
        await this.core.render(container, qrData);
        this.logger.debug('QR Code renderizado no container');
        this.emit(EVENTS.QR_RENDERED, { container, qrData });
      } catch (error) {
        this.logger.error('Erro ao renderizar QR Code:', error);
        this.emit(EVENTS.ERROR, error);
        throw error;
      }
    }

    async export(format = 'png', exportOptions = {}, qrData = null) {
      try {
        const result = await this.core.export(format, exportOptions, qrData);
        this.logger.debug(`QR Code exportado em formato ${format}`);
        this.emit(EVENTS.QR_EXPORTED, { format, result });
        return result;
      } catch (error) {
        this.logger.error('Erro ao exportar QR Code:', error);
        this.emit(EVENTS.ERROR, error);
        throw error;
      }
    }

    async download(filename = 'qrcode', format = 'png', exportOptions = {}) {
      try {
        await this.core.download(filename, format, exportOptions);
        this.logger.info(`QR Code baixado como ${filename}.${format}`);
      } catch (error) {
        this.logger.error('Erro ao baixar QR Code:', error);
        this.emit(EVENTS.ERROR, error);
        throw error;
      }
    }

    listPlugins() {
      return this.core.listPlugins();
    }

    getCurrentInfo() {
      return this.core.getCurrentQRInfo();
    }

    clear() {
      this.core.clear();
      this.logger.debug('QR Code atual limpo');
    }

    async cleanup() {
      try {
        await this.core.cleanup();
        this.removeAllListeners();
        this.initialized = false;
        this.logger.info('Recursos da biblioteca limpos');
      } catch (error) {
        this.logger.error('Erro ao limpar recursos:', error);
        throw error;
      }
    }
  }

  // === FUNÇÕES DE CONVENIÊNCIA ===
  async function initializeQRLibrary(options = {}) {
    const {
      autoLoadPlugins = true,
      pluginConfig = {},
      ...libraryOptions
    } = options;
    const library = new QRLibrary(libraryOptions);
    await library.initialize();

    if (autoLoadPlugins) {
      // Registrar plugins disponíveis
      try {
        await library.registerPlugin(new QRCodeGeneratorPlugin());
      } catch (e) {
        console.warn(
          'Plugin qrcode-generator não pôde ser carregado:',
          e.message
        );
      }

      try {
        await library.registerPlugin(new QRCodeStylingPlugin());
      } catch (e) {
        console.warn(
          'Plugin qr-code-styling não pôde ser carregado:',
          e.message
        );
      }

      // Carregar plugins
      try {
        await library.loadPlugin(
          'qrcode-generator',
          pluginConfig['qrcode-generator'] || {}
        );
      } catch (e) {
        console.warn('Erro ao carregar qrcode-generator:', e.message);
      }

      try {
        await library.loadPlugin(
          'qr-code-styling',
          pluginConfig['qr-code-styling'] || {}
        );
      } catch (e) {
        console.warn('Erro ao carregar qr-code-styling:', e.message);
      }
    }

    return library;
  }

  async function quickGenerateQR(data, options = {}) {
    const library = await initializeQRLibrary();
    return await library.generate({ data, ...options });
  }

  async function quickRenderQR(container, data, options = {}) {
    const library = await initializeQRLibrary();
    const qrResult = await library.generate({ data, ...options });
    await library.render(container, qrResult);
  }

  // Instância padrão
  const qrLibrary = new QRLibrary();

  // Exportar para o global
  global.QRLibraryModular = {
    EXPORT_FORMATS,
    QR_TYPES,
    ERROR_CORRECTION_LEVELS,
    ENCODING_MODES,
    DOT_TYPES,
    CORNER_SQUARE_TYPES,
    CORNER_DOT_TYPES,
    GRADIENT_TYPES,
    EVENTS,
    DEFAULT_QR_OPTIONS,
    EventEmitter,
    Logger,
    IQRPlugin,
    PluginManager,
    QRLibraryCore,
    QRCodeGeneratorPlugin,
    QRCodeStylingPlugin,
    QRLibrary,
    qrLibrary,
    initializeQRLibrary,
    quickGenerateQR,
    quickRenderQR,
  };
})(typeof window !== 'undefined' ? window : global);
