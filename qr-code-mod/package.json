{"name": "qr-library-modular-standalone", "version": "1.0.0", "description": "QR Library Modular - Versão Standalone Autônoma", "main": "index.html", "type": "module", "scripts": {"start": "node server.js", "serve": "python3 -m http.server 8080", "serve-python2": "python -m SimpleHTTPServer 8080", "dev": "node server.js"}, "keywords": ["qr-code", "qr", "modular", "generator", "standalone", "offline", "library"], "author": "QR Library Team", "license": "MIT", "dependencies": {"cors": "^2.8.5", "express": "^4.18.2"}, "engines": {"node": ">=14.0.0"}, "repository": {"type": "git", "url": "https://github.com/qr-library/qr-library-modular"}, "homepage": "https://github.com/qr-library/qr-library-modular#readme", "bugs": {"url": "https://github.com/qr-library/qr-library-modular/issues"}}