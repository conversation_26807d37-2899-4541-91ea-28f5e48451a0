<!DOCTYPE html>
<html lang="pt-BR">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>QR Library Modular - Exemplos de API</title>
    <style>
      body {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto,
          sans-serif;
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
        background-color: #f5f5f5;
      }

      .container {
        background: white;
        border-radius: 12px;
        padding: 30px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        margin-bottom: 20px;
      }

      h1 {
        color: #333;
        text-align: center;
        margin-bottom: 30px;
      }

      .example {
        margin-bottom: 40px;
        padding: 20px;
        border: 1px solid #e0e0e0;
        border-radius: 8px;
        background: #fafafa;
      }

      .example h3 {
        color: #555;
        margin-bottom: 15px;
      }

      .code-block {
        background: #2d3748;
        color: #e2e8f0;
        padding: 15px;
        border-radius: 6px;
        font-family: 'Monaco', 'Men<PERSON>', 'Ubuntu Mono', monospace;
        font-size: 14px;
        overflow-x: auto;
        margin: 10px 0;
      }

      .result {
        margin-top: 15px;
        padding: 15px;
        background: white;
        border-radius: 6px;
        border: 1px solid #ddd;
      }

      button {
        background: #4299e1;
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 6px;
        cursor: pointer;
        font-size: 14px;
        margin: 5px;
      }

      button:hover {
        background: #3182ce;
      }

      .qr-display {
        text-align: center;
        margin: 15px 0;
      }

      .qr-display canvas {
        border: 1px solid #ddd;
        border-radius: 6px;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <h1>🔧 QR Library Modular - Exemplos de API</h1>

      <div class="example">
        <h3>1. QR Code Básico</h3>
        <div class="code-block">
          const qrLib = new QRLibraryModular(); const qrCode =
          qrLib.generate('Hello World!');
        </div>
        <button onclick="example1()">Executar Exemplo 1</button>
        <div id="result1" class="result" style="display: none">
          <div class="qr-display"></div>
        </div>
      </div>

      <div class="example">
        <h3>2. QR Code com Cores Personalizadas</h3>
        <div class="code-block">
          const qrCode = qrLib.generate('https://example.com', { width: 300,
          height: 300, dotsOptions: { color: '#ff6b6b', type: 'rounded' },
          backgroundOptions: { color: '#f8f9fa' } });
        </div>
        <button onclick="example2()">Executar Exemplo 2</button>
        <div id="result2" class="result" style="display: none">
          <div class="qr-display"></div>
        </div>
      </div>

      <div class="example">
        <h3>3. QR Code com Gradiente</h3>
        <div class="code-block">
          const qrCode = qrLib.generate('Gradiente Incrível!', { width: 300,
          height: 300, dotsOptions: { gradient: { type: 'linear', rotation: 45,
          colorStops: [ { offset: 0, color: '#667eea' }, { offset: 1, color:
          '#764ba2' } ] } } });
        </div>
        <button onclick="example3()">Executar Exemplo 3</button>
        <div id="result3" class="result" style="display: none">
          <div class="qr-display"></div>
        </div>
      </div>

      <div class="example">
        <h3>4. Download Programático</h3>
        <div class="code-block">
          // Gerar QR code const qrCode = qrLib.generate('Download Test'); //
          Download em diferentes formatos qrLib.download(qrCode, 'meu-qr-png',
          'png'); qrLib.download(qrCode, 'meu-qr-jpeg', 'jpeg');
          qrLib.download(qrCode, 'meu-qr-svg', 'svg');
        </div>
        <button onclick="example4()">Executar Exemplo 4</button>
        <div id="result4" class="result" style="display: none">
          <div class="qr-display"></div>
          <p>Clique nos botões abaixo para fazer download:</p>
          <button onclick="downloadPNG()">Download PNG</button>
          <button onclick="downloadJPEG()">Download JPEG</button>
          <button onclick="downloadSVG()">Download SVG</button>
        </div>
      </div>

      <div class="example">
        <h3>5. Verificar Plugins Disponíveis</h3>
        <div class="code-block">
          // Listar plugins disponíveis console.log('Plugins disponíveis:',
          qrLib.getAvailablePlugins()); // Verificar se um plugin específico
          está disponível console.log('QRCodeStyling disponível:',
          qrLib.hasPlugin('qr-code-styling')); console.log('QRCodeGenerator
          disponível:', qrLib.hasPlugin('qrcode-generator'));
        </div>
        <button onclick="example5()">Executar Exemplo 5</button>
        <div id="result5" class="result" style="display: none">
          <p>Verifique o console do navegador para ver os resultados.</p>
        </div>
      </div>
    </div>

    <!-- Dependências -->
    <script src="qrcode.js"></script>
    <script src="qr-code-styling/lib/qr-code-styling.js"></script>
    <script src="qr-library-modular.js"></script>

    <script>
      let qrLib;
      let currentQRCode;

      // Inicializar a biblioteca quando a página carregar
      window.addEventListener('load', function () {
        // Aguardar um pouco para garantir que todas as dependências foram carregadas
        setTimeout(() => {
          if (
            typeof QRLibraryModular !== 'undefined' &&
            QRLibraryModular.QRLibrary
          ) {
            qrLib = new QRLibraryModular.QRLibrary({
              enableLogging: false,
              logLevel: 0,
            });

            // Registrar e carregar plugins
            if (QRLibraryModular.QRCodeGeneratorPlugin) {
              const generatorPlugin =
                new QRLibraryModular.QRCodeGeneratorPlugin();
              qrLib.registerPlugin(generatorPlugin);
              qrLib.loadPlugin('qrcode-generator');
            }
            if (QRLibraryModular.QRCodeStylingPlugin) {
              const stylingPlugin = new QRLibraryModular.QRCodeStylingPlugin();
              qrLib.registerPlugin(stylingPlugin);
              qrLib.loadPlugin('qr-code-styling');
            }

            // QR Library Modular inicializada silenciosamente
            // Plugins carregados: qrLib.listPlugins()
          } else {
            console.error('QRLibraryModular.QRLibrary não está disponível');
          }
        }, 100);
      });

      function example1() {
        const qrCode = qrLib.generate('Hello World!', {
          data: 'Hello World!',
          width: 300,
          height: 300,
        });
        currentQRCode = qrCode;

        const result = document.getElementById('result1');
        const display = result.querySelector('.qr-display');
        display.innerHTML = '';
        display.appendChild(qrCode);
        result.style.display = 'block';
      }

      function example2() {
        const qrCode = qrLib.generate('https://example.com', {
          data: 'https://example.com',
          width: 300,
          height: 300,
          dotsOptions: {
            color: '#ff6b6b',
            type: 'rounded',
          },
          backgroundOptions: {
            color: '#f8f9fa',
          },
        });

        const result = document.getElementById('result2');
        const display = result.querySelector('.qr-display');
        display.innerHTML = '';
        display.appendChild(qrCode);
        result.style.display = 'block';
      }

      function example3() {
        const qrCode = qrLib.generate('Gradiente Incrível!', {
          data: 'Gradiente Incrível!',
          width: 300,
          height: 300,
          dotsOptions: {
            gradient: {
              type: 'linear',
              rotation: 45,
              colorStops: [
                { offset: 0, color: '#667eea' },
                { offset: 1, color: '#764ba2' },
              ],
            },
          },
        });

        const result = document.getElementById('result3');
        const display = result.querySelector('.qr-display');
        display.innerHTML = '';
        display.appendChild(qrCode);
        result.style.display = 'block';
      }

      function example4() {
        currentQRCode = qrLib.generate('Download Test', {
          data: 'Download Test',
          width: 300,
          height: 300,
          dotsOptions: {
            color: '#4299e1',
            type: 'square',
          },
        });

        const result = document.getElementById('result4');
        const display = result.querySelector('.qr-display');
        display.innerHTML = '';
        display.appendChild(currentQRCode);
        result.style.display = 'block';
      }

      function downloadPNG() {
        if (currentQRCode) {
          qrLib.download(currentQRCode, 'meu-qr-png', 'png');
        }
      }

      function downloadJPEG() {
        if (currentQRCode) {
          qrLib.download(currentQRCode, 'meu-qr-jpeg', 'jpeg');
        }
      }

      function downloadSVG() {
        if (currentQRCode) {
          qrLib.download(currentQRCode, 'meu-qr-svg', 'svg');
        }
      }

      function example5() {
        console.log('=== Informações dos Plugins ===');
        console.log('Plugins disponíveis:', qrLib.listPlugins());
        console.log('Informações atuais:', qrLib.getCurrentInfo());

        const result = document.getElementById('result5');
        result.style.display = 'block';

        alert(
          'Informações dos plugins foram exibidas no console. Abra as ferramentas de desenvolvedor (F12) para ver.'
        );
      }
    </script>
  </body>
</html>
