<!DOCTYPE html>
<html lang="pt-BR">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>QR Code Library - Interface Modular</title>
    <link rel="icon" type="image/svg+xml" href="favicon.svg" />
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto,
          Oxygen, Ubuntu, Cantarell, 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
          sans-serif;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        font-size: 16px;
        display: flex;
        flex-direction: column;
        min-height: 100vh;
        background-color: #f5f5f5;
      }

      main {
        flex-grow: 1;
      }

      .button,
      button,
      input,
      select {
        font: inherit;
      }

      .button,
      button,
      select,
      input[type='color'],
      input[type='checkbox'] {
        cursor: pointer;
      }

      .button,
      button {
        padding: 10px;
        transition: background-color 0.2s ease-out;
        background-color: #d7d7d7;
        border: 1px solid #000;
        border-radius: 0;
      }

      .button:hover,
      button:hover {
        background-color: #c7c7c7;
      }

      .button:active,
      button:active {
        background-color: #b7b7b7;
      }

      input,
      select,
      textarea {
        padding: 10px;
        border: 1px solid #000;
        border-radius: 0;
        background-color: #fff;
      }

      input[type='color'] {
        width: 50px;
        height: 40px;
        padding: 0;
        border: 1px solid #000;
      }

      input[type='number'] {
        width: 80px;
      }

      input[type='range'] {
        width: 100%;
      }

      label {
        display: block;
        margin-bottom: 5px;
        font-weight: 500;
      }

      .space-between-container {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10px;
      }

      .accordion {
        background-color: #d7d7d7;
        padding: 10px;
        width: 100%;
        text-align: left;
        border: none;
        outline: none;
        transition: background-color 0.4s;
      }

      .accordion:hover {
        background-color: #c7c7c7;
      }

      .accordion:after {
        content: '+';
        font-size: 13px;
        color: #777;
        float: right;
        margin-left: 5px;
      }

      .accordion.accordion--open:after,
      .accordion.active:after {
        content: '−';
      }

      .panel {
        padding: 0;
        background-color: white;
        overflow: hidden;
        transition: all 0.3s ease-out;
        display: none;
        grid-template-columns: 1fr 1fr;
        gap: 20px;
      }

      .panel.panel--open {
        display: grid;
        padding: 15px;
        max-height: none;
        overflow: visible;
      }

      .container {
        display: flex;
        flex-direction: column;
        align-items: center;
      }

      .container > * {
        width: 100%;
        max-width: 1008px;
      }

      .row {
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
      }

      .row--qr-header {
        align-items: center;
      }

      .row--body {
        align-items: start;
      }

      .col {
        padding: 20px;
        flex: 1;
        min-width: 0;
      }

      .qr-form {
        flex: 2;
        min-width: 400px;
        display: flex;
        flex-direction: column;
      }

      .qr-code-container {
        flex: 1;
        min-width: 300px;
        max-width: 400px;
        position: sticky;
        top: 5px;
      }

      .buttons-container {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        justify-content: space-between;
      }

      #form-image-file {
        flex-grow: 1;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        width: 150px;
      }

      .qr-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: #f5f5f5;
      }

      .qr-description {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: #fff;
        min-height: 30vh;
        justify-content: center;
        font-size: 21px;
      }

      .qr-code canvas {
        max-width: 300px !important;
        width: 100%;
      }

      .qr-download-group {
        display: flex;
        justify-content: center;
        align-items: center;
      }

      .qr-download-group select {
        border-left: 1px solid #000;
      }

      .options-export-group {
        margin-top: 20px;
      }

      #form-data {
        width: 100%;
      }

      .hide {
        height: 0;
        width: 0;
        opacity: 0;
        overflow: hidden;
        position: absolute;
        z-index: -1;
      }

      .qr-description__lib-name {
        font-size: 48px;
      }

      a.white {
        color: #f5f5f5;
        text-decoration: none;
        transition: color 0.4s;
      }

      a.white:hover {
        color: #cdcdcd;
      }

      a.black {
        color: #000000;
        text-decoration: none;
        transition: color 0.4s;
      }

      a.black:hover {
        color: #7a7a7a;
      }

      /* Responsividade */
      @media (max-width: 768px) {
        .row--body {
          flex-direction: column;
        }

        .qr-form {
          min-width: auto;
        }

        .qr-code-container {
          min-width: auto;
          max-width: none;
          order: -1;
        }
      }

      /* Status do plugin */
      .plugin-status {
        margin: 10px 0;
        padding: 10px;
        border-radius: 4px;
        font-size: 14px;
      }

      .plugin-status.success {
        background-color: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
      }

      .plugin-status.error {
        background-color: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
      }

      .plugin-status.warning {
        background-color: #fff3cd;
        color: #856404;
        border: 1px solid #ffeaa7;
      }
    </style>
  </head>
  <body>
    <header class="container qr-header">
      <div class="row row--qr-header">
        <div class="col">
          <h2>QR Code Library - Interface Modular</h2>
        </div>
        <a
          target="_blank"
          rel="noopener"
          class="col white"
          href="https://www.npmjs.com/package/qr-code-styling"
          >npm v1.8.3</a
        >
        <a
          target="_blank"
          rel="noopener"
          class="col white"
          href="https://github.com/kozakdenys/qr-code-styling"
          >GitHub</a
        >
      </div>
    </header>
    <main>
      <section class="container qr-description" id="qr-description">
        <div class="col">
          <h1 class="qr-description__lib-name">QR Library Modular</h1>
          <p>Arquitetura Modular e Extensível</p>
          <p>API Programática Unificada</p>
          <div id="plugin-status" class="plugin-status"></div>
        </div>
      </section>
      <section class="container">
        <div class="row row--body">
          <form class="col qr-form" id="form">
            <!-- Status dos plugins será inserido aqui -->

            <!-- Main Options - Sempre aberto -->
            <button type="button" class="accordion accordion--open">
              Main Options
            </button>
            <div class="panel panel--open">
              <label for="form-data">Data</label>
              <textarea
                id="form-data"
                type="text"
                value="https://qr-code-styling.com"
              >
https://qr-code-styling.com</textarea
              >

              <label for="form-image-file">Image File</label>
              <div class="buttons-container">
                <input id="form-image-file" type="file" accept="image/*" />
                <button type="button" id="form-image-file-clear">Cancel</button>
              </div>

              <label for="form-width">Width</label>
              <input id="form-width" type="number" value="300" />

              <label for="form-height">Height</label>
              <input id="form-height" type="number" value="300" />

              <label for="form-margin">Margin</label>
              <input id="form-margin" type="number" value="0" />
            </div>

            <!-- Dots Options -->
            <button type="button" class="accordion">Dots Options</button>
            <div class="panel">
              <label for="form-dots-style">Dots Style</label>
              <div>
                <select id="form-dots-style">
                  <option value="square">Square</option>
                  <option value="dots">Dots</option>
                  <option value="rounded">Rounded</option>
                  <option value="extra-rounded" selected>Extra rounded</option>
                  <option value="classy">Classy</option>
                  <option value="classy-rounded">Classy rounded</option>
                </select>
              </div>

              <label>Color Type</label>
              <div class="space-between-container">
                <div style="flex-grow: 1">
                  <input
                    id="form-dots-color-type-single"
                    type="radio"
                    name="dots-color-type"
                    checked
                  />
                  <label for="form-dots-color-type-single">Single color</label>
                </div>
                <div style="flex-grow: 1">
                  <input
                    id="form-dots-color-type-gradient"
                    type="radio"
                    name="dots-color-type"
                  />
                  <label for="form-dots-color-type-gradient"
                    >Color gradient</label
                  >
                </div>
              </div>

              <label class="dots-single-color" for="form-dots-color"
                >Dots Color</label
              >
              <input
                class="dots-single-color"
                id="form-dots-color"
                type="color"
                value="#6a1a4c"
              />

              <div class="dots-gradient-options" style="display: none">
                <label>Gradient Type</label>
                <div class="space-between-container">
                  <div style="flex-grow: 1">
                    <input
                      id="form-dots-gradient-type-linear"
                      type="radio"
                      name="dots-gradient-type"
                      checked
                    />
                    <label for="form-dots-gradient-type-linear">Linear</label>
                  </div>
                  <div style="flex-grow: 1">
                    <input
                      id="form-dots-gradient-type-radial"
                      type="radio"
                      name="dots-gradient-type"
                    />
                    <label for="form-dots-gradient-type-radial">Radial</label>
                  </div>
                </div>

                <label>Dots Gradient</label>
                <div class="space-between-container">
                  <input
                    id="form-dots-gradient-color1"
                    type="color"
                    value="#6a1a4c"
                  />
                  <input
                    id="form-dots-gradient-color2"
                    type="color"
                    value="#6a1a4c"
                  />
                </div>

                <label for="form-dots-gradient-rotation">Rotation</label>
                <input
                  id="form-dots-gradient-rotation"
                  type="number"
                  value="0"
                />
              </div>
            </div>

            <!-- Corners Square Options -->
            <button type="button" class="accordion">
              Corners Square Options
            </button>
            <div class="panel">
              <label for="form-corners-square-style"
                >Corners Square Style</label
              >
              <div>
                <select id="form-corners-square-style">
                  <option value="">None</option>
                  <option value="square">Square</option>
                  <option value="dot">Dot</option>
                  <option value="extra-rounded" selected>Extra rounded</option>
                </select>
              </div>

              <label for="form-corners-square-color"
                >Corners Square Color</label
              >
              <div class="buttons-container">
                <input
                  id="form-corners-square-color"
                  type="color"
                  value="#000000"
                />
                <button type="button" id="button-clear-corners-square-color">
                  Clear
                </button>
              </div>
            </div>

            <!-- Corners Dot Options -->
            <button type="button" class="accordion">Corners Dot Options</button>
            <div class="panel">
              <label for="form-corners-dot-style">Corners Dot Style</label>
              <div>
                <select id="form-corners-dot-style">
                  <option value="">None</option>
                  <option value="square">Square</option>
                  <option value="dot">Dot</option>
                </select>
              </div>

              <label for="form-corners-dot-color">Corners Dot Color</label>
              <div class="buttons-container">
                <input
                  id="form-corners-dot-color"
                  type="color"
                  value="#000000"
                />
                <button type="button" id="button-clear-corners-dot-color">
                  Clear
                </button>
              </div>
            </div>

            <!-- Background Options -->
            <button type="button" class="accordion">Background Options</button>
            <div class="panel">
              <label for="form-background-color">Background Color</label>
              <input id="form-background-color" type="color" value="#ffffff" />
            </div>

            <!-- Image Options -->
            <button type="button" class="accordion">Image Options</button>
            <div class="panel">
              <label for="form-image-hide-background-dots"
                >Hide Background Dots</label
              >
              <input
                id="form-image-hide-background-dots"
                type="checkbox"
                checked
              />

              <label for="form-image-size">Image Size</label>
              <input
                id="form-image-size"
                type="number"
                step="0.1"
                min="0"
                max="1"
                value="0.4"
              />

              <label for="form-image-margin">Image Margin</label>
              <input id="form-image-margin" type="number" value="0" />
            </div>

            <!-- QR Options -->
            <button type="button" class="accordion">QR Options</button>
            <div class="panel">
              <label for="form-qr-type-number">Type Number</label>
              <input
                id="form-qr-type-number"
                type="number"
                min="0"
                max="40"
                value="0"
              />

              <label for="form-qr-mode">Mode</label>
              <select id="form-qr-mode">
                <option value="Numeric">Numeric</option>
                <option value="Alphanumeric">Alphanumeric</option>
                <option value="Byte" selected>Byte</option>
                <option value="Kanji">Kanji</option>
              </select>

              <label for="form-qr-error-correction-level"
                >Error Correction Level</label
              >
              <select id="form-qr-error-correction-level">
                <option value="L">L</option>
                <option value="M">M</option>
                <option value="Q" selected>Q</option>
                <option value="H">H</option>
              </select>
            </div>

            <!-- Exemplos Pré-configurados -->
            <button type="button" class="accordion">
              Exemplos Pré-configurados
            </button>
            <div class="panel">
              <label>Escolha um exemplo:</label>
              <div style="display: flex; flex-direction: column; gap: 10px">
                <button type="button" class="button" id="example-classic">
                  Clássico
                </button>
                <button type="button" class="button" id="example-gradient">
                  Gradiente Colorido
                </button>
                <button type="button" class="button" id="example-rounded">
                  Pontos Arredondados
                </button>
                <button type="button" class="button" id="example-classy">
                  Estilo Elegante
                </button>
                <button type="button" class="button" id="example-modern">
                  Moderno com Cantos
                </button>
              </div>
            </div>

            <div class="options-export-group">
              <a class="button" id="export-options">Export Options as JSON</a>
            </div>
          </form>

          <div class="col qr-code-container">
            <div class="qr-code" id="qr-code-generated"></div>
            <div class="qr-download-group">
              <button id="qr-download">Download</button>
              <label class="hide" for="qr-extension">Extension</label>
              <select id="qr-extension">
                <option value="png" selected>PNG</option>
                <option value="jpeg">JPEG</option>
                <option value="svg">SVG</option>
              </select>
            </div>
          </div>
        </div>
      </section>
    </main>

    <!-- Dependências -->
    <script src="./qrcode.js"></script>
    <script src="./qr-code-styling/lib/qr-code-styling.js"></script>

    <!-- Nova biblioteca modular -->
    <script src="./qr-library-modular.js"></script>

    <script>
      // Inicializar a biblioteca modular
      let qrLibrary = null;
      let currentQRResult = null;

      async function initializeLibrary() {
        try {
          const statusDiv = document.getElementById('plugin-status');
          statusDiv.textContent = 'Inicializando biblioteca modular...';
          statusDiv.className = 'plugin-status warning';

          // Usar a função de conveniência para inicializar
          qrLibrary = await QRLibraryModular.initializeQRLibrary({
            enableLogging: false,
            logLevel: 0, // ERROR apenas
          });

          // Verificar plugins carregados
          const plugins = qrLibrary.listPlugins();
          const loadedPlugins = plugins.filter((p) => p.loaded);

          if (loadedPlugins.length > 0) {
            statusDiv.textContent = `Biblioteca inicializada! Plugins carregados: ${loadedPlugins
              .map((p) => p.name)
              .join(', ')}`;
            statusDiv.className = 'plugin-status success';
          } else {
            statusDiv.textContent =
              'Biblioteca inicializada, mas nenhum plugin foi carregado';
            statusDiv.className = 'plugin-status warning';
          }

          // Gerar QR inicial
          await generateQR();
        } catch (error) {
          console.error('Erro ao inicializar biblioteca:', error);
          const statusDiv = document.getElementById('plugin-status');
          statusDiv.textContent = `Erro ao inicializar: ${error.message}`;
          statusDiv.className = 'plugin-status error';
        }
      }

      async function generateQR() {
        if (!qrLibrary) return;

        try {
          const options = getFormOptions();
          currentQRResult = await qrLibrary.generate(options);

          const container = document.getElementById('qr-code-generated');
          await qrLibrary.render(container, currentQRResult);
        } catch (error) {
          console.error('Erro ao gerar QR:', error);
        }
      }

      function getFormOptions() {
        const options = {
          data:
            document.getElementById('form-data').value ||
            'https://qr-code-styling.com',
          width: parseInt(document.getElementById('form-width').value) || 300,
          height: parseInt(document.getElementById('form-height').value) || 300,
          margin: parseInt(document.getElementById('form-margin').value) || 0,
          qrOptions: {
            typeNumber:
              parseInt(document.getElementById('form-qr-type-number').value) ||
              0,
            mode: document.getElementById('form-qr-mode').value || 'Byte',
            errorCorrectionLevel:
              document.getElementById('form-qr-error-correction-level').value ||
              'Q',
          },
          dotsOptions: {
            type:
              document.getElementById('form-dots-style').value ||
              'extra-rounded',
            color:
              document.getElementById('form-dots-color').value || '#6a1a4c',
          },
          backgroundOptions: {
            color:
              document.getElementById('form-background-color').value ||
              '#ffffff',
          },
          imageOptions: {
            hideBackgroundDots: document.getElementById(
              'form-image-hide-background-dots'
            ).checked,
            imageSize:
              parseFloat(document.getElementById('form-image-size').value) ||
              0.4,
            margin:
              parseInt(document.getElementById('form-image-margin').value) || 0,
          },
        };

        // Corners Square Options
        const cornersSquareType = document.getElementById(
          'form-corners-square-style'
        ).value;
        if (cornersSquareType) {
          options.cornersSquareOptions = {
            type: cornersSquareType,
            color:
              document.getElementById('form-corners-square-color').value ||
              '#000000',
          };
        }

        // Corners Dot Options
        const cornersDotType = document.getElementById(
          'form-corners-dot-style'
        ).value;
        if (cornersDotType) {
          options.cornersDotOptions = {
            type: cornersDotType,
            color:
              document.getElementById('form-corners-dot-color').value ||
              '#000000',
          };
        }

        // Gradiente para dots
        if (document.getElementById('form-dots-color-type-gradient').checked) {
          const gradientType = document.getElementById(
            'form-dots-gradient-type-linear'
          ).checked
            ? 'linear'
            : 'radial';
          options.dotsOptions.gradient = {
            type: gradientType,
            rotation:
              parseFloat(
                document.getElementById('form-dots-gradient-rotation').value
              ) || 0,
            color1:
              document.getElementById('form-dots-gradient-color1').value ||
              '#6a1a4c',
            color2:
              document.getElementById('form-dots-gradient-color2').value ||
              '#6a1a4c',
          };
          delete options.dotsOptions.color;
        }

        return options;
      }

      // Função para alternar visibilidade de gradiente
      function toggleGradientOptions() {
        const isGradient = document.getElementById(
          'form-dots-color-type-gradient'
        ).checked;
        const singleColorElements =
          document.querySelectorAll('.dots-single-color');
        const gradientElements = document.querySelectorAll(
          '.dots-gradient-options'
        );

        singleColorElements.forEach((el) => {
          el.style.display = isGradient ? 'none' : 'block';
        });

        gradientElements.forEach((el) => {
          el.style.display = isGradient ? 'block' : 'none';
        });
      }

      // Função para accordion
      function setupAccordion() {
        const accordions = document.querySelectorAll('.accordion');
        accordions.forEach((accordion) => {
          accordion.addEventListener('click', function () {
            this.classList.toggle('accordion--open');
            const panel = this.nextElementSibling;
            panel.classList.toggle('panel--open');
          });
        });
      }

      // Exemplos pré-configurados
      const examples = {
        classic: {
          margin: 10,
          dotsOptions: { type: 'square', color: '#000000' },
          backgroundOptions: { color: '#ffffff' },
          cornersSquareOptions: { type: 'square', color: '#000000' },
          cornersDotOptions: { type: 'square', color: '#000000' },
        },
        gradient: {
          dotsOptions: {
            type: 'rounded',
            gradient: {
              type: 'linear',
              rotation: 0,
              color1: '#667eea',
              color2: '#764ba2',
            },
          },
          backgroundOptions: { color: '#ffffff' },
          cornersSquareOptions: { type: 'extra-rounded', color: '#667eea' },
        },
        rounded: {
          dotsOptions: { type: 'rounded', color: '#4267b2' },
          backgroundOptions: { color: '#e9ebee' },
          cornersSquareOptions: { type: 'extra-rounded', color: '#4267b2' },
          cornersDotOptions: { type: 'dot', color: '#4267b2' },
        },
        classy: {
          dotsOptions: { type: 'classy', color: '#2c3e50' },
          backgroundOptions: { color: '#ecf0f1' },
          cornersSquareOptions: { type: 'extra-rounded', color: '#e74c3c' },
          cornersDotOptions: { type: 'dot', color: '#e74c3c' },
        },
        modern: {
          dotsOptions: { type: 'extra-rounded', color: '#8e44ad' },
          backgroundOptions: { color: '#f8f9fa' },
          cornersSquareOptions: { type: 'extra-rounded', color: '#3498db' },
          cornersDotOptions: { type: 'dot', color: '#e67e22' },
        },
      };

      function applyExample(exampleKey) {
        const example = examples[exampleKey];
        if (!example) return;

        // Aplicar margem se definida no preset
        if (example.margin !== undefined) {
          document.getElementById('form-margin').value = example.margin;
        }

        // Aplicar configurações do exemplo
        if (example.dotsOptions) {
          if (example.dotsOptions.type) {
            document.getElementById('form-dots-style').value =
              example.dotsOptions.type;
          }

          if (example.dotsOptions.gradient) {
            // Ativar gradiente
            document.getElementById(
              'form-dots-color-type-gradient'
            ).checked = true;
            document.getElementById(
              'form-dots-color-type-single'
            ).checked = false;

            // Configurar gradiente
            const isLinear = example.dotsOptions.gradient.type === 'linear';
            document.getElementById('form-dots-gradient-type-linear').checked =
              isLinear;
            document.getElementById('form-dots-gradient-type-radial').checked =
              !isLinear;

            document.getElementById('form-dots-gradient-color1').value =
              example.dotsOptions.gradient.color1;
            document.getElementById('form-dots-gradient-color2').value =
              example.dotsOptions.gradient.color2;
            document.getElementById('form-dots-gradient-rotation').value =
              example.dotsOptions.gradient.rotation || 0;
          } else if (example.dotsOptions.color) {
            // Ativar cor sólida
            document.getElementById(
              'form-dots-color-type-single'
            ).checked = true;
            document.getElementById(
              'form-dots-color-type-gradient'
            ).checked = false;
            document.getElementById('form-dots-color').value =
              example.dotsOptions.color;
          }
        }

        if (example.backgroundOptions?.color) {
          document.getElementById('form-background-color').value =
            example.backgroundOptions.color;
        }

        if (example.cornersSquareOptions) {
          document.getElementById('form-corners-square-style').value =
            example.cornersSquareOptions.type || '';
          if (example.cornersSquareOptions.color) {
            document.getElementById('form-corners-square-color').value =
              example.cornersSquareOptions.color;
          }
        }

        if (example.cornersDotOptions) {
          document.getElementById('form-corners-dot-style').value =
            example.cornersDotOptions.type || '';
          if (example.cornersDotOptions.color) {
            document.getElementById('form-corners-dot-color').value =
              example.cornersDotOptions.color;
          }
        }

        // Atualizar visibilidade dos controles
        toggleGradientOptions();

        // Regenerar QR Code
        generateQR();
      }

      // Event listeners
      document
        .getElementById('form-data')
        .addEventListener('input', generateQR);
      document
        .getElementById('form-width')
        .addEventListener('input', generateQR);
      document
        .getElementById('form-height')
        .addEventListener('input', generateQR);
      document
        .getElementById('form-margin')
        .addEventListener('input', generateQR);
      document
        .getElementById('form-dots-style')
        .addEventListener('change', generateQR);
      document
        .getElementById('form-dots-color')
        .addEventListener('change', generateQR);
      document
        .getElementById('form-background-color')
        .addEventListener('change', generateQR);
      document
        .getElementById('form-corners-square-style')
        .addEventListener('change', generateQR);
      document
        .getElementById('form-corners-square-color')
        .addEventListener('change', generateQR);
      document
        .getElementById('form-corners-dot-style')
        .addEventListener('change', generateQR);
      document
        .getElementById('form-corners-dot-color')
        .addEventListener('change', generateQR);
      document
        .getElementById('form-qr-type-number')
        .addEventListener('input', generateQR);
      document
        .getElementById('form-qr-mode')
        .addEventListener('change', generateQR);
      document
        .getElementById('form-qr-error-correction-level')
        .addEventListener('change', generateQR);
      document
        .getElementById('form-image-hide-background-dots')
        .addEventListener('change', generateQR);
      document
        .getElementById('form-image-size')
        .addEventListener('input', generateQR);
      document
        .getElementById('form-image-margin')
        .addEventListener('input', generateQR);

      // Gradiente listeners
      document
        .getElementById('form-dots-color-type-single')
        .addEventListener('change', () => {
          toggleGradientOptions();
          generateQR();
        });
      document
        .getElementById('form-dots-color-type-gradient')
        .addEventListener('change', () => {
          toggleGradientOptions();
          generateQR();
        });
      document
        .getElementById('form-dots-gradient-type-linear')
        .addEventListener('change', generateQR);
      document
        .getElementById('form-dots-gradient-type-radial')
        .addEventListener('change', generateQR);
      document
        .getElementById('form-dots-gradient-color1')
        .addEventListener('change', generateQR);
      document
        .getElementById('form-dots-gradient-color2')
        .addEventListener('change', generateQR);
      document
        .getElementById('form-dots-gradient-rotation')
        .addEventListener('input', generateQR);

      // Exemplos
      document
        .getElementById('example-classic')
        .addEventListener('click', () => applyExample('classic'));
      document
        .getElementById('example-gradient')
        .addEventListener('click', () => applyExample('gradient'));
      document
        .getElementById('example-rounded')
        .addEventListener('click', () => applyExample('rounded'));
      document
        .getElementById('example-classy')
        .addEventListener('click', () => applyExample('classy'));
      document
        .getElementById('example-modern')
        .addEventListener('click', () => applyExample('modern'));

      // Download
      document
        .getElementById('qr-download')
        .addEventListener('click', async () => {
          if (!qrLibrary) return;

          const format = document.getElementById('qr-extension').value;
          try {
            await qrLibrary.download('qrcode-modular', format);
          } catch (error) {
            console.error('Erro ao baixar:', error);
          }
        });

      // Export JSON
      document
        .getElementById('export-options')
        .addEventListener('click', () => {
          const options = getFormOptions();
          const json = JSON.stringify(options, null, 2);
          const blob = new Blob([json], { type: 'application/json' });
          const url = URL.createObjectURL(blob);
          const link = document.createElement('a');
          link.href = url;
          link.download = 'qr-options.json';
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
          URL.revokeObjectURL(url);
        });

      // Clear buttons
      document
        .getElementById('button-clear-corners-square-color')
        .addEventListener('click', () => {
          document.getElementById('form-corners-square-style').value = '';
          generateQR();
        });

      document
        .getElementById('button-clear-corners-dot-color')
        .addEventListener('click', () => {
          document.getElementById('form-corners-dot-style').value = '';
          generateQR();
        });

      document
        .getElementById('form-image-file-clear')
        .addEventListener('click', () => {
          document.getElementById('form-image-file').value = '';
          generateQR();
        });

      // Inicializar quando a página carregar
      document.addEventListener('DOMContentLoaded', () => {
        setupAccordion();
        toggleGradientOptions();
        initializeLibrary();
      });
    </script>
  </body>
</html>
