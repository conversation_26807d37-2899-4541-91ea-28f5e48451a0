# QR Library - Gui<PERSON> da API Programática

## Instalação e Configuração

### Uso no Navegador

```html
<!-- Dependências -->
<script src="./qrcode.js"></script>
<script src="./qr-code-styling/lib/qr-code-styling.js"></script>

<!-- Biblioteca Modular -->
<script src="./lib/qr-library-modular.js"></script>
```

### Inicialização Básica

```javascript
// Inicialização automática com plugins
const qrLibrary = await QRLibraryModular.initializeQRLibrary();

// Ou inicialização manual
const qrLibrary = new QRLibraryModular.QRLibrary();
await qrLibrary.initialize();

// Registrar plugins manualmente
await qrLibrary.registerPlugin(new QRLibraryModular.QRCodeGeneratorPlugin());
await qrLibrary.registerPlugin(new QRLibraryModular.QRCodeStylingPlugin());
```

## API Principal

### Geração de QR Code

```javascript
// Geração básica
const qrResult = await qrLibrary.generate({
  data: 'https://exemplo.com.br'
});

// Geração com opções avançadas
const qrResult = await qrLibrary.generate({
  data: 'https://exemplo.com.br',
  width: 400,
  height: 400,
  margin: 10,
  dotsOptions: {
    type: 'rounded',
    color: '#6a1a4c'
  },
  backgroundOptions: {
    color: '#ffffff'
  },
  cornersSquareOptions: {
    type: 'extra-rounded',
    color: '#000000'
  }
});
```

### Renderização no DOM

```javascript
// Renderizar em um container
const container = document.getElementById('qr-container');
await qrLibrary.render(container, qrResult);

// Renderizar com geração automática
await qrLibrary.render(container); // Usa o último QR gerado
```

### Exportação e Download

```javascript
// Exportar como blob
const blob = await qrLibrary.export('png');
const svgBlob = await qrLibrary.export('svg');

// Download direto
await qrLibrary.download('meu-qr-code', 'png');
await qrLibrary.download('qr-vetorial', 'svg');

// Exportar com opções
const blob = await qrLibrary.export('png', {
  quality: 0.9,
  width: 800,
  height: 800
});
```

## Opções de Configuração

### Opções Básicas

```javascript
{
  data: 'string',           // Dados para o QR Code (obrigatório)
  width: 300,               // Largura em pixels
  height: 300,              // Altura em pixels
  margin: 0,                // Margem em pixels
  type: 'canvas'            // Tipo de renderização
}
```

### Opções de QR

```javascript
{
  qrOptions: {
    typeNumber: 0,          // Tamanho automático (0) ou 1-40
    mode: 'Byte',           // Numeric, Alphanumeric, Byte, Kanji
    errorCorrectionLevel: 'Q' // L, M, Q, H
  }
}
```

### Opções de Pontos

```javascript
{
  dotsOptions: {
    type: 'rounded',        // square, dots, rounded, extra-rounded, classy, classy-rounded
    color: '#000000',       // Cor sólida
    // OU gradiente
    gradient: {
      type: 'linear',       // linear, radial
      rotation: 0,          // Rotação em graus
      color1: '#ff0000',    // Cor inicial
      color2: '#0000ff'     // Cor final
    }
  }
}
```

### Opções de Fundo

```javascript
{
  backgroundOptions: {
    color: '#ffffff',       // Cor de fundo
    // OU gradiente
    gradient: {
      type: 'radial',
      rotation: 45,
      color1: '#f0f0f0',
      color2: '#ffffff'
    }
  }
}
```

### Opções de Cantos

```javascript
{
  cornersSquareOptions: {
    type: 'extra-rounded',  // square, dot, extra-rounded
    color: '#000000'
  },
  cornersDotOptions: {
    type: 'dot',           // square, dot
    color: '#000000'
  }
}
```

### Opções de Imagem

```javascript
{
  image: 'data:image/png;base64,...', // URL ou data URL da imagem
  imageOptions: {
    hideBackgroundDots: true,         // Ocultar pontos atrás da imagem
    imageSize: 0.4,                   // Tamanho da imagem (0-1)
    margin: 0                         // Margem da imagem
  }
}
```

## Funções de Conveniência

### Geração Rápida

```javascript
// Gerar QR rapidamente
const qrResult = await QRLibraryModular.quickGenerateQR('https://exemplo.com');

// Gerar e renderizar em uma linha
await QRLibraryModular.quickRenderQR(
  document.getElementById('container'),
  'https://exemplo.com',
  { width: 200, height: 200 }
);
```

### Instância Global

```javascript
// Usar instância global pré-configurada
const qrLibrary = QRLibraryModular.qrLibrary;
await qrLibrary.initialize();
```

## Sistema de Eventos

### Escutar Eventos

```javascript
qrLibrary.on('qr-generated', (result) => {
  console.log('QR Code gerado:', result);
});

qrLibrary.on('qr-rendered', ({ container, qrData }) => {
  console.log('QR Code renderizado no container:', container);
});

qrLibrary.on('qr-exported', ({ format, result }) => {
  console.log(`QR Code exportado em ${format}:`, result);
});

qrLibrary.on('plugin-loaded', ({ plugin }) => {
  console.log('Plugin carregado:', plugin);
});

qrLibrary.on('error', (error) => {
  console.error('Erro na biblioteca:', error);
});
```

## Gerenciamento de Plugins

### Listar Plugins

```javascript
const plugins = qrLibrary.listPlugins();
console.log('Plugins disponíveis:', plugins);

// Verificar plugins carregados
const loadedPlugins = plugins.filter(p => p.loaded);
console.log('Plugins carregados:', loadedPlugins);
```

### Informações do QR Atual

```javascript
const info = qrLibrary.getCurrentInfo();
console.log('Plugin usado:', info.plugin);
console.log('Opções:', info.options);
console.log('Capacidades:', info.capabilities);
```

## Exemplos Práticos

### QR Code Simples

```javascript
const qrLibrary = await QRLibraryModular.initializeQRLibrary();

const qrResult = await qrLibrary.generate({
  data: 'https://meusite.com.br'
});

await qrLibrary.render(document.getElementById('qr-container'), qrResult);
```

### QR Code Estilizado

```javascript
const qrResult = await qrLibrary.generate({
  data: 'https://meusite.com.br',
  width: 300,
  height: 300,
  dotsOptions: {
    type: 'rounded',
    gradient: {
      type: 'linear',
      rotation: 45,
      color1: '#667eea',
      color2: '#764ba2'
    }
  },
  backgroundOptions: {
    color: '#ffffff'
  },
  cornersSquareOptions: {
    type: 'extra-rounded',
    color: '#667eea'
  }
});

await qrLibrary.render(document.getElementById('qr-container'), qrResult);
await qrLibrary.download('qr-estilizado', 'png');
```

### QR Code com Imagem

```javascript
// Converter imagem para data URL primeiro
const fileInput = document.getElementById('image-input');
const file = fileInput.files[0];

const reader = new FileReader();
reader.onload = async function(e) {
  const qrResult = await qrLibrary.generate({
    data: 'https://meusite.com.br',
    width: 400,
    height: 400,
    image: e.target.result,
    imageOptions: {
      hideBackgroundDots: true,
      imageSize: 0.3,
      margin: 5
    },
    dotsOptions: {
      type: 'extra-rounded',
      color: '#2c3e50'
    }
  });
  
  await qrLibrary.render(document.getElementById('qr-container'), qrResult);
};
reader.readAsDataURL(file);
```

## Tratamento de Erros

```javascript
try {
  const qrResult = await qrLibrary.generate({
    data: 'https://exemplo.com'
  });
  
  await qrLibrary.render(container, qrResult);
  
} catch (error) {
  console.error('Erro ao gerar QR Code:', error);
  
  // Tratar diferentes tipos de erro
  if (error.message.includes('Plugin não foi inicializado')) {
    await qrLibrary.initialize();
  } else if (error.message.includes('Container deve ser um elemento HTML')) {
    console.error('Container inválido fornecido');
  }
}
```

## Limpeza de Recursos

```javascript
// Limpar QR atual
qrLibrary.clear();

// Limpar todos os recursos
await qrLibrary.cleanup();
```

## Constantes Disponíveis

```javascript
// Formatos de exportação
QRLibraryModular.EXPORT_FORMATS.PNG
QRLibraryModular.EXPORT_FORMATS.JPEG
QRLibraryModular.EXPORT_FORMATS.SVG
QRLibraryModular.EXPORT_FORMATS.WEBP

// Tipos de pontos
QRLibraryModular.DOT_TYPES.SQUARE
QRLibraryModular.DOT_TYPES.DOTS
QRLibraryModular.DOT_TYPES.ROUNDED
QRLibraryModular.DOT_TYPES.EXTRA_ROUNDED
QRLibraryModular.DOT_TYPES.CLASSY
QRLibraryModular.DOT_TYPES.CLASSY_ROUNDED

// Níveis de correção de erro
QRLibraryModular.ERROR_CORRECTION_LEVELS.L
QRLibraryModular.ERROR_CORRECTION_LEVELS.M
QRLibraryModular.ERROR_CORRECTION_LEVELS.Q
QRLibraryModular.ERROR_CORRECTION_LEVELS.H

// Eventos
QRLibraryModular.EVENTS.QR_GENERATED
QRLibraryModular.EVENTS.QR_RENDERED
QRLibraryModular.EVENTS.QR_EXPORTED
QRLibraryModular.EVENTS.PLUGIN_LOADED
QRLibraryModular.EVENTS.ERROR
```
