# Guia de Migração - QR Library Modular

## Visão Geral

Este guia ajuda na migração do código existente que usa as bibliotecas `qrcode-generator` e `qr-code-styling` diretamente para a nova QR Library Modular.

## Benefícios da Migração

- ✅ **API Unificada**: Uma única interface para ambas as bibliotecas
- ✅ **Seleção Automática**: Escolha automática do melhor plugin para cada caso
- ✅ **Compatibilidade**: Mantém todas as funcionalidades existentes
- ✅ **Extensibilidade**: Fácil adição de novas bibliotecas no futuro
- ✅ **Melhor Manutenção**: Código mais organizado e testado

## Migração do qrcode-generator

### Antes (qrcode-generator)

```javascript
// Código antigo
const qr = qrcode(0, 'M');
qr.addData('https://exemplo.com');
qr.make();

const canvas = document.createElement('canvas');
const ctx = canvas.getContext('2d');
const modules = qr.modules;
const tileW = canvas.width / modules.length;
const tileH = canvas.height / modules.length;

for (let row = 0; row < modules.length; row++) {
  for (let col = 0; col < modules[row].length; col++) {
    ctx.fillStyle = modules[row][col] ? '#000' : '#fff';
    ctx.fillRect(col * tileW, row * tileH, tileW, tileH);
  }
}

document.getElementById('container').appendChild(canvas);
```

### Depois (QR Library Modular)

```javascript
// Código novo
const qrLibrary = await QRLibraryModular.initializeQRLibrary();

const qrResult = await qrLibrary.generate({
  data: 'https://exemplo.com',
  width: 300,
  height: 300,
  qrOptions: {
    errorCorrectionLevel: 'M'
  }
});

await qrLibrary.render(document.getElementById('container'), qrResult);
```

## Migração do qr-code-styling

### Antes (qr-code-styling)

```javascript
// Código antigo
const qrCodeStyling = new QRCodeStyling({
  width: 300,
  height: 300,
  data: 'https://exemplo.com',
  dotsOptions: {
    color: '#6a1a4c',
    type: 'rounded'
  },
  backgroundOptions: {
    color: '#ffffff'
  },
  cornersSquareOptions: {
    color: '#000000',
    type: 'extra-rounded'
  }
});

qrCodeStyling.append(document.getElementById('container'));

// Download
qrCodeStyling.download({
  name: 'qr-code',
  extension: 'png'
});
```

### Depois (QR Library Modular)

```javascript
// Código novo
const qrLibrary = await QRLibraryModular.initializeQRLibrary();

const qrResult = await qrLibrary.generate({
  data: 'https://exemplo.com',
  width: 300,
  height: 300,
  dotsOptions: {
    color: '#6a1a4c',
    type: 'rounded'
  },
  backgroundOptions: {
    color: '#ffffff'
  },
  cornersSquareOptions: {
    color: '#000000',
    type: 'extra-rounded'
  }
});

await qrLibrary.render(document.getElementById('container'), qrResult);

// Download
await qrLibrary.download('qr-code', 'png');
```

## Mapeamento de Opções

### qrcode-generator → QR Library Modular

| qrcode-generator | QR Library Modular |
|------------------|-------------------|
| `qrcode(typeNumber, errorCorrectionLevel)` | `qrOptions: { typeNumber, errorCorrectionLevel }` |
| `addData(data)` | `data: 'string'` |
| `make()` | `await generate()` |
| Manual canvas rendering | `await render(container)` |

### qr-code-styling → QR Library Modular

| qr-code-styling | QR Library Modular |
|-----------------|-------------------|
| `new QRCodeStyling(options)` | `await generate(options)` |
| `append(container)` | `await render(container)` |
| `download({ name, extension })` | `await download(name, extension)` |
| `getRawData(extension)` | `await export(extension)` |

## Exemplos de Migração Completos

### Exemplo 1: QR Code Básico

**Antes:**
```javascript
// Com qrcode-generator
function createBasicQR() {
  const qr = qrcode(0, 'M');
  qr.addData('https://meusite.com');
  qr.make();
  
  const img = qr.createImgTag(4, 0);
  document.getElementById('qr-container').innerHTML = img;
}
```

**Depois:**
```javascript
// Com QR Library Modular
async function createBasicQR() {
  const qrLibrary = await QRLibraryModular.initializeQRLibrary();
  
  const qrResult = await qrLibrary.generate({
    data: 'https://meusite.com',
    qrOptions: {
      errorCorrectionLevel: 'M'
    }
  });
  
  await qrLibrary.render(document.getElementById('qr-container'), qrResult);
}
```

### Exemplo 2: QR Code Estilizado

**Antes:**
```javascript
// Com qr-code-styling
function createStyledQR() {
  const qrCode = new QRCodeStyling({
    width: 400,
    height: 400,
    data: 'https://meusite.com',
    dotsOptions: {
      color: '#667eea',
      type: 'extra-rounded'
    },
    backgroundOptions: {
      color: '#ffffff'
    },
    cornersSquareOptions: {
      color: '#667eea',
      type: 'extra-rounded'
    }
  });
  
  qrCode.append(document.getElementById('qr-container'));
  
  // Download
  document.getElementById('download-btn').onclick = () => {
    qrCode.download({
      name: 'styled-qr',
      extension: 'png'
    });
  };
}
```

**Depois:**
```javascript
// Com QR Library Modular
async function createStyledQR() {
  const qrLibrary = await QRLibraryModular.initializeQRLibrary();
  
  const qrResult = await qrLibrary.generate({
    data: 'https://meusite.com',
    width: 400,
    height: 400,
    dotsOptions: {
      color: '#667eea',
      type: 'extra-rounded'
    },
    backgroundOptions: {
      color: '#ffffff'
    },
    cornersSquareOptions: {
      color: '#667eea',
      type: 'extra-rounded'
    }
  });
  
  await qrLibrary.render(document.getElementById('qr-container'), qrResult);
  
  // Download
  document.getElementById('download-btn').onclick = async () => {
    await qrLibrary.download('styled-qr', 'png');
  };
}
```

### Exemplo 3: QR Code com Gradiente

**Antes:**
```javascript
// Com qr-code-styling
const qrCode = new QRCodeStyling({
  width: 300,
  height: 300,
  data: 'https://exemplo.com',
  dotsOptions: {
    gradient: {
      type: 'linear',
      rotation: 45,
      colorStops: [
        { offset: 0, color: '#667eea' },
        { offset: 1, color: '#764ba2' }
      ]
    }
  }
});
```

**Depois:**
```javascript
// Com QR Library Modular
const qrResult = await qrLibrary.generate({
  data: 'https://exemplo.com',
  width: 300,
  height: 300,
  dotsOptions: {
    gradient: {
      type: 'linear',
      rotation: 45,
      color1: '#667eea',
      color2: '#764ba2'
    }
  }
});
```

## Diferenças Importantes

### 1. Inicialização Assíncrona

**Importante**: A nova biblioteca requer inicialização assíncrona:

```javascript
// ❌ Não funciona
const qrLibrary = new QRLibraryModular.QRLibrary();
qrLibrary.generate(); // Erro!

// ✅ Correto
const qrLibrary = await QRLibraryModular.initializeQRLibrary();
await qrLibrary.generate();
```

### 2. Todas as Operações são Assíncronas

```javascript
// ❌ Código síncrono antigo
const qr = qrcode(0, 'M');
qr.addData('data');
qr.make();

// ✅ Código assíncrono novo
const qrResult = await qrLibrary.generate({ data: 'data' });
await qrLibrary.render(container, qrResult);
```

### 3. Formato de Gradientes Simplificado

**Antes (qr-code-styling):**
```javascript
gradient: {
  type: 'linear',
  rotation: 45,
  colorStops: [
    { offset: 0, color: '#ff0000' },
    { offset: 1, color: '#0000ff' }
  ]
}
```

**Depois (QR Library Modular):**
```javascript
gradient: {
  type: 'linear',
  rotation: 45,
  color1: '#ff0000',
  color2: '#0000ff'
}
```

### 4. Download Simplificado

**Antes:**
```javascript
qrCode.download({
  name: 'qr-code',
  extension: 'png'
});
```

**Depois:**
```javascript
await qrLibrary.download('qr-code', 'png');
```

## Checklist de Migração

### ✅ Preparação
- [ ] Incluir a biblioteca modular no projeto
- [ ] Manter as dependências originais (qrcode.js, qr-code-styling)
- [ ] Atualizar imports/scripts no HTML

### ✅ Código
- [ ] Substituir instanciação direta por `initializeQRLibrary()`
- [ ] Converter todas as chamadas para async/await
- [ ] Atualizar formato de opções (especialmente gradientes)
- [ ] Substituir métodos de download
- [ ] Atualizar tratamento de erros

### ✅ Testes
- [ ] Testar geração de QR codes básicos
- [ ] Testar QR codes estilizados
- [ ] Testar downloads em diferentes formatos
- [ ] Verificar compatibilidade com navegadores alvo
- [ ] Validar performance

### ✅ Otimização
- [ ] Reutilizar instância da biblioteca quando possível
- [ ] Implementar cache de QR codes se necessário
- [ ] Otimizar carregamento de plugins

## Solução de Problemas Comuns

### Erro: "Plugin não foi inicializado"
```javascript
// Certifique-se de inicializar antes de usar
const qrLibrary = await QRLibraryModular.initializeQRLibrary();
```

### Erro: "Container deve ser um elemento HTML"
```javascript
// Verifique se o elemento existe
const container = document.getElementById('qr-container');
if (container) {
  await qrLibrary.render(container, qrResult);
}
```

### QR Code não aparece
```javascript
// Aguarde a renderização completa
await qrLibrary.render(container, qrResult);
console.log('QR Code renderizado com sucesso');
```

### Download não funciona
```javascript
// Certifique-se de gerar o QR antes do download
const qrResult = await qrLibrary.generate({ data: 'test' });
await qrLibrary.download('test-qr', 'png');
```

## Suporte e Recursos

- **Documentação Completa**: `docs/GUIA_API.md`
- **Arquitetura**: `docs/ARQUITETURA_MODULAR.md`
- **Exemplos**: `demo-modular.html`
- **Testes**: `tests/e2e/`

A migração para a QR Library Modular oferece uma API mais consistente, melhor manutenibilidade e preparação para futuras extensões, mantendo toda a funcionalidade existente.
