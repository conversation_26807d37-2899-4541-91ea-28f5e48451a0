# QR Library Modular - Versão Standalone

Uma implementação modular e extensível para geração de QR codes, completamente autônoma e pronta para uso offline.

## 🚀 Características

- **100% Offline**: Funciona completamente sem conexão com a internet
- **Modular**: Arquitetura baseada em plugins extensível
- **Interface Completa**: Interface web responsiva com todas as funcionalidades
- **API Programática**: Acesso completo via JavaScript
- **Múltiplos Formatos**: Suporte para PNG, JPEG e SVG
- **Exemplos Pré-configurados**: Templates prontos para uso
- **Zero Dependências Externas**: Todas as bibliotecas incluídas

## 📦 Conteúdo do Pacote

```
qr-code-mod/
├── index.html                    # Interface principal
├── qr-library-modular.js        # Biblioteca compilada
├── qrcode.js                     # qrcode-generator
├── qr-code-styling/             # Biblioteca qr-code-styling completa
├── server.js                     # Servidor local
├── package.json                  # Configuração do projeto
├── docs/                         # Documentação
│   ├── ARQUITETURA_MODULAR.md
│   ├── GUIA_API.md
│   └── GUIA_MIGRACAO.md
└── README.md                     # Este arquivo
```

## 🏃‍♂️ Como Executar

### Opção 1: Servidor Node.js (Recomendado)

```bash
# Instalar dependências
npm install

# Iniciar servidor
npm start
```

Acesse: http://localhost:8080

### Opção 2: Servidor Python

```bash
# Python 3
npm run serve

# Python 2
npm run serve-python2
```

Acesse: http://localhost:8080

### Opção 3: Servidor Local Simples

Você pode usar qualquer servidor HTTP local:

```bash
# Com Python 3
python3 -m http.server 8080

# Com Python 2
python -m SimpleHTTPServer 8080

# Com PHP
php -S localhost:8080

# Com Ruby
ruby -run -e httpd . -p 8080
```

## 🎯 Funcionalidades

### Interface Web
- **Geração de QR Codes**: Texto, URLs, dados personalizados
- **Personalização Visual**: Cores, gradientes, formas
- **Exemplos Pré-configurados**: Templates prontos
- **Download**: PNG, JPEG, SVG
- **Preview em Tempo Real**: Visualização instantânea
- **Interface Responsiva**: Funciona em desktop e mobile

### API Programática

```javascript
// Inicializar a biblioteca
const qrLib = new QRLibraryModular();

// Gerar QR code simples
const qrCode = qrLib.generate('https://example.com');

// Gerar com opções avançadas
const styledQR = qrLib.generate('Meu texto', {
  width: 300,
  height: 300,
  dotsOptions: {
    color: '#000000',
    type: 'rounded'
  },
  backgroundOptions: {
    color: '#ffffff'
  }
});

// Download programático
qrLib.download(qrCode, 'meu-qr-code', 'png');
```

## 📚 Documentação

- **[Guia da API](docs/GUIA_API.md)**: Referência completa da API
- **[Arquitetura Modular](docs/ARQUITETURA_MODULAR.md)**: Como a biblioteca funciona
- **[Guia de Migração](docs/GUIA_MIGRACAO.md)**: Como migrar de outras bibliotecas

## 🔧 Configuração

### Personalização do Servidor

Edite `server.js` para:
- Alterar a porta (padrão: 8080)
- Configurar CORS
- Adicionar rotas personalizadas
- Configurar headers HTTP

### Personalização da Interface

Edite `index.html` para:
- Modificar o layout
- Adicionar novos exemplos
- Personalizar estilos
- Integrar com outras aplicações

## 🌟 Exemplos de Uso

### 1. QR Code Básico
```javascript
const qr = qrLib.generate('Hello World');
```

### 2. QR Code com Logo
```javascript
const qr = qrLib.generate('https://example.com', {
  image: 'path/to/logo.png',
  imageOptions: {
    hideBackgroundDots: true,
    imageSize: 0.4
  }
});
```

### 3. QR Code com Gradiente
```javascript
const qr = qrLib.generate('Gradient QR', {
  dotsOptions: {
    gradient: {
      type: 'linear',
      rotation: 45,
      colorStops: [
        { offset: 0, color: '#ff0000' },
        { offset: 1, color: '#0000ff' }
      ]
    }
  }
});
```

## 🚀 Deploy

Esta versão standalone pode ser facilmente deployada em:

- **Servidores Web**: Apache, Nginx
- **Plataformas Cloud**: Vercel, Netlify, GitHub Pages
- **Containers**: Docker
- **Servidores Node.js**: Heroku, Railway, DigitalOcean

## 📄 Licença

MIT License - veja o arquivo LICENSE para detalhes.

## 🤝 Contribuição

Contribuições são bem-vindas! Por favor, leia o guia de contribuição antes de submeter PRs.

## 📞 Suporte

- **Issues**: Reporte bugs e solicite features
- **Documentação**: Consulte os arquivos em `docs/`
- **Exemplos**: Veja a interface web para exemplos práticos
