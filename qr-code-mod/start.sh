#!/bin/bash

# QR Library Modular Standalone - Script de Inicialização
# Este script facilita a inicialização da aplicação

echo "🚀 QR Library Modular Standalone"
echo "================================="
echo ""

# Verificar se Node.js está instalado
if ! command -v node &> /dev/null; then
    echo "❌ Node.js não encontrado!"
    echo "Por favor, instale Node.js (versão 14 ou superior) em https://nodejs.org"
    echo ""
    echo "Alternativas sem Node.js:"
    echo "- Python 3: python3 -m http.server 8080"
    echo "- Python 2: python -m SimpleHTTPServer 8080"
    echo "- PHP: php -S localhost:8080"
    exit 1
fi

# Verificar se as dependências estão instaladas
if [ ! -d "node_modules" ]; then
    echo "📦 Instalando dependências..."
    npm install
    echo ""
fi

# Iniciar o servidor
echo "🌟 Iniciando servidor..."
echo "📱 A aplicação estará disponível em: http://localhost:8080"
echo "🔧 Health check em: http://localhost:8080/health"
echo ""
echo "Para parar o servidor, pressione Ctrl+C"
echo ""

npm start
